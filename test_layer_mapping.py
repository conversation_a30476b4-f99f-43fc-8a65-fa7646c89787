#!/usr/bin/env python3
"""
测试知识蒸馏层映射关系的脚本
"""

import torch
from ultralytics import YOLO
from Globals import *

def test_layer_mapping():
    """测试层映射关系是否正确"""
    print("🔄 测试知识蒸馏层映射关系...")
    
    try:
        # 加载标准YOLOv8模型
        print(f"📥 加载标准教师模型: {path_teacher}")
        teacher_model = YOLO(path_teacher)
        teacher_dict = teacher_model.model.state_dict()
        
        # 加载知识蒸馏模型架构
        print(f"📥 加载知识蒸馏模型架构: {model_file}")
        distill_model = YOLO(model_file)
        distill_dict = distill_model.model.state_dict()
        
        print(f"📊 标准模型参数数量: {len(teacher_dict)}")
        print(f"📊 蒸馏模型参数数量: {len(distill_dict)}")
        
        # 分析层结构
        print("\n🔍 分析标准模型层结构:")
        teacher_layers = set()
        for name in teacher_dict.keys():
            parts = name.split('.')
            if len(parts) >= 3 and parts[2].isdigit():
                layer_num = int(parts[2])
                teacher_layers.add(layer_num)
        
        teacher_layers = sorted(teacher_layers)
        print(f"标准模型层编号: {teacher_layers}")
        
        print("\n🔍 分析蒸馏模型层结构:")
        distill_layers = set()
        for name in distill_dict.keys():
            parts = name.split('.')
            if len(parts) >= 3 and parts[2].isdigit():
                layer_num = int(parts[2])
                distill_layers.add(layer_num)
        
        distill_layers = sorted(distill_layers)
        print(f"蒸馏模型层编号: {distill_layers}")
        
        # 验证映射关系
        print("\n🔍 验证教师层映射关系:")
        print("标准层 -> 蒸馏教师层")
        for i, teacher_layer in enumerate(teacher_peer_list):
            if i < len(teacher_layers):
                std_layer = teacher_layers[i]
                print(f"  {std_layer:2d} -> {teacher_layer:2d}")
            else:
                print(f"  ?? -> {teacher_layer:2d} (超出范围)")
        
        print("\n🔍 验证学生层映射关系:")
        print("标准层 -> 蒸馏学生层")
        for i, student_layer in enumerate(student_peer_list):
            if i < len(teacher_layers):
                std_layer = teacher_layers[i]
                print(f"  {std_layer:2d} -> {student_layer:2d}")
            else:
                print(f"  ?? -> {student_layer:2d} (超出范围)")
        
        # 检查形状匹配
        print("\n🔍 检查权重形状匹配:")
        shape_matches = 0
        shape_mismatches = 0
        
        for i, std_layer in enumerate(teacher_layers[:5]):  # 只检查前5层
            if i < len(teacher_peer_list):
                teacher_target_layer = teacher_peer_list[i]
                
                # 查找标准模型中该层的权重
                std_weight_name = None
                for name in teacher_dict.keys():
                    if f"model.{std_layer}." in name and "weight" in name:
                        std_weight_name = name
                        break
                
                # 查找蒸馏模型中对应层的权重
                distill_weight_name = None
                if std_weight_name:
                    distill_weight_name = std_weight_name.replace(f"model.{std_layer}.", f"model.{teacher_target_layer}.")
                
                if std_weight_name and distill_weight_name:
                    if distill_weight_name in distill_dict:
                        std_shape = teacher_dict[std_weight_name].shape
                        distill_shape = distill_dict[distill_weight_name].shape
                        
                        if std_shape == distill_shape:
                            shape_matches += 1
                            print(f"  ✅ 层{std_layer}->{teacher_target_layer}: {std_shape}")
                        else:
                            shape_mismatches += 1
                            print(f"  ❌ 层{std_layer}->{teacher_target_layer}: {std_shape} != {distill_shape}")
                    else:
                        print(f"  ⚠️  层{std_layer}->{teacher_target_layer}: 目标层不存在")
        
        print(f"\n📊 形状匹配统计: ✅{shape_matches} ❌{shape_mismatches}")
        
        return shape_matches > shape_mismatches
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def suggest_fixes():
    """建议修复方案"""
    print("\n💡 建议的修复步骤:")
    print("1. 确认标准YOLOv8模型层数为0-21（共22层）")
    print("2. 确认知识蒸馏模型教师层为1-22，学生层为23-44")
    print("3. 运行权重转换: python convert_weights.py")
    print("4. 设置 phase=1 开始蒸馏训练")
    print("5. 监控训练损失，确保收敛")

if __name__ == "__main__":
    print("🚀 开始测试知识蒸馏层映射关系...")
    
    success = test_layer_mapping()
    
    if success:
        print("\n🎉 层映射关系测试通过！")
    else:
        print("\n❌ 层映射关系存在问题，需要修复。")
        suggest_fixes()
