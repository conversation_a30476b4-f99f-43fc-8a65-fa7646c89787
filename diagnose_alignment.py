#!/usr/bin/env python3
"""
诊断知识蒸馏特征层对齐的脚本
"""

import torch
from ultralytics import YOLO
from Globals import *

def diagnose_feature_alignment():
    """诊断教师和学生特征层对齐情况"""
    print("🔍 开始诊断知识蒸馏特征层对齐...")
    
    try:
        # 1. 加载标准YOLOv8模型
        print(f"\n📥 加载标准教师模型: {path_teacher}")
        std_model = YOLO(path_teacher)
        
        # 2. 加载知识蒸馏模型
        print(f"📥 加载知识蒸馏模型: {model_file}")
        distill_model = YOLO(model_file)
        
        # 3. 创建测试输入
        test_input = torch.randn(1, 3, 640, 640)
        print(f"📊 测试输入形状: {test_input.shape}")
        
        # 4. 获取标准模型的特征层输出
        print("\n🔍 分析标准模型特征层...")
        std_model.model.eval()
        with torch.no_grad():
            # 获取中间特征
            y = []
            x = test_input
            for i, m in enumerate(std_model.model.model[:-1]):  # 除了检测头
                if m.f != -1:
                    x = y[m.f] if isinstance(m.f, int) else [x if j == -1 else y[j] for j in m.f]
                x = m(x)
                y.append(x if m.i in std_model.model.save else None)
                
                # 记录关键特征层
                if i in [15, 18, 21]:  # P3, P4, P5特征层
                    print(f"  标准模型层{i}: {x.shape if isinstance(x, torch.Tensor) else [t.shape for t in x]}")
        
        # 5. 获取知识蒸馏模型的特征层输出
        print("\n🔍 分析知识蒸馏模型特征层...")
        distill_model.model.eval()
        with torch.no_grad():
            y_distill = []
            x_distill = test_input
            for i, m in enumerate(distill_model.model.model[:-1]):  # 除了检测头
                if m.f != -1:
                    x_distill = y_distill[m.f] if isinstance(m.f, int) else [x_distill if j == -1 else y_distill[j] for j in m.f]
                x_distill = m(x_distill)
                y_distill.append(x_distill if m.i in distill_model.model.save else None)
                
                # 记录教师特征层
                if i in [16, 19, 22]:  # 教师P3, P4, P5特征层
                    print(f"  教师层{i}: {x_distill.shape if isinstance(x_distill, torch.Tensor) else [t.shape for t in x_distill]}")
                
                # 记录学生特征层
                if i in [38, 41, 44]:  # 学生P3, P4, P5特征层
                    print(f"  学生层{i}: {x_distill.shape if isinstance(x_distill, torch.Tensor) else [t.shape for t in x_distill]}")
        
        # 6. 比较特征层形状
        print("\n📊 特征层形状对比:")
        print("标准模型 -> 教师层 -> 学生层")
        
        # 检查P3特征层
        if y[15] is not None and y_distill[16] is not None and y_distill[38] is not None:
            std_shape = y[15].shape
            teacher_shape = y_distill[16].shape
            student_shape = y_distill[38].shape
            print(f"P3: {std_shape} -> {teacher_shape} -> {student_shape}")
            if std_shape == teacher_shape == student_shape:
                print("  ✅ P3特征层对齐正确")
            else:
                print("  ❌ P3特征层对齐错误")
        
        # 检查P4特征层
        if y[18] is not None and y_distill[19] is not None and y_distill[41] is not None:
            std_shape = y[18].shape
            teacher_shape = y_distill[19].shape
            student_shape = y_distill[41].shape
            print(f"P4: {std_shape} -> {teacher_shape} -> {student_shape}")
            if std_shape == teacher_shape == student_shape:
                print("  ✅ P4特征层对齐正确")
            else:
                print("  ❌ P4特征层对齐错误")
        
        # 检查P5特征层
        if y[21] is not None and y_distill[22] is not None and y_distill[44] is not None:
            std_shape = y[21].shape
            teacher_shape = y_distill[22].shape
            student_shape = y_distill[44].shape
            print(f"P5: {std_shape} -> {teacher_shape} -> {student_shape}")
            if std_shape == teacher_shape == student_shape:
                print("  ✅ P5特征层对齐正确")
            else:
                print("  ❌ P5特征层对齐错误")
        
        # 7. 测试检测头输出
        print("\n🔍 测试检测头输出...")
        
        # 标准模型检测头输出
        std_output = std_model.model(test_input)
        print(f"标准模型输出: {[t.shape for t in std_output] if isinstance(std_output, (list, tuple)) else std_output.shape}")
        
        # 知识蒸馏模型检测头输出
        distill_output = distill_model.model(test_input)
        print(f"蒸馏模型输出: {[t.shape for t in distill_output] if isinstance(distill_output, (list, tuple)) else distill_output.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ 诊断失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_weight_loading():
    """检查权重加载情况"""
    print("\n🔍 检查权重加载情况...")
    
    try:
        # 检查权重文件是否存在
        import os
        if not os.path.exists(path_teacher_prepare):
            print(f"❌ 教师权重文件不存在: {path_teacher_prepare}")
            return False
        
        # 加载权重
        teacher_weights = torch.load(path_teacher_prepare, map_location='cpu')
        print(f"📊 教师权重文件包含 {len(teacher_weights)} 个参数")
        
        # 加载模型
        model = YOLO(model_file)
        model_dict = model.model.state_dict()
        print(f"📊 目标模型包含 {len(model_dict)} 个参数")
        
        # 检查关键层的权重
        key_layers = ['model.16.', 'model.19.', 'model.22.', 'model.38.', 'model.41.', 'model.44.', 'model.45.', 'model.46.']
        
        for key_layer in key_layers:
            teacher_params = [name for name in teacher_weights.keys() if key_layer in name]
            model_params = [name for name in model_dict.keys() if key_layer in name]
            
            print(f"\n层 {key_layer}:")
            print(f"  教师权重: {len(teacher_params)} 个参数")
            print(f"  模型参数: {len(model_params)} 个参数")
            
            if teacher_params and model_params:
                # 检查第一个参数的形状
                first_teacher = teacher_params[0]
                first_model = model_params[0]
                if first_teacher in teacher_weights and first_model in model_dict:
                    teacher_shape = teacher_weights[first_teacher].shape
                    model_shape = model_dict[first_model].shape
                    if teacher_shape == model_shape:
                        print(f"  ✅ 形状匹配: {teacher_shape}")
                    else:
                        print(f"  ❌ 形状不匹配: {teacher_shape} vs {model_shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ 权重检查失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始知识蒸馏诊断...")
    
    # 诊断特征层对齐
    alignment_ok = diagnose_feature_alignment()
    
    # 检查权重加载
    weights_ok = check_weight_loading()
    
    print("\n" + "="*60)
    print("诊断结果总结")
    print("="*60)
    print(f"特征层对齐: {'✅ 正常' if alignment_ok else '❌ 异常'}")
    print(f"权重加载: {'✅ 正常' if weights_ok else '❌ 异常'}")
    
    if alignment_ok and weights_ok:
        print("\n🎉 诊断通过！知识蒸馏配置正确。")
        print("💡 如果训练损失仍然异常，可能需要:")
        print("   1. 调整学习率")
        print("   2. 检查数据集标注")
        print("   3. 调整蒸馏超参数")
    else:
        print("\n❌ 发现问题，需要修复后再训练。")
    print("="*60)
