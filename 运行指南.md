# YOLO知识蒸馏运行指南

## 🎯 目标
将YOLO知识蒸馏从fruit数据集切换到COCO128数据集，确保教师-学生层正确映射，损失函数正常收敛到2.0以下。

## 📋 已完成的修改

### 1. 配置文件修改
- **Globals.py**: 
  - 模型文件: `yolov8-lite-fruit.yaml` → `yolov8-lite.yaml`
  - 数据集: `fruit.yaml` → `coco128.yaml`
  - 测试图像: `orange.jpg` → `bus.jpg`
  - 类别数: 3 → 80 (COCO)
  - 优化了蒸馏超参数以降低损失

- **yolov8-lite.yaml**:
  - 设置 `nc: 80` (COCO类别数)
  - 保持双架构: 教师层(1-22) + 学生层(23-44)

### 2. 层映射关系修复
- **标准YOLOv8模型**: 层0-21 (共22层)
- **知识蒸馏教师层**: 层1-22 (映射关系: 0→1, 1→2, ..., 21→22)
- **知识蒸馏学生层**: 层23-44 (映射关系: 0→23, 1→24, ..., 21→44)
- **检测头映射**: 标准模型层22 → 教师检测头层45

### 3. 权重转换优化
- 修复了 `test_for_exchange_teacher()` 函数
- 正确处理检测头权重映射
- 添加了详细的转换日志和错误处理

## 🚀 运行步骤

### 步骤1: 权重转换 (Phase 0)
```bash
# 设置 Globals.py 中 phase = 0
python main.py
```
或者单独运行权重转换:
```bash
python convert_weights.py
```

### 步骤2: 开始训练 (Phase 1)
```bash
# 设置 Globals.py 中 phase = 1
python main.py
```

### 步骤3: 快速测试 (可选)
```bash
python quick_test.py
```

## 📊 关键参数设置

### 训练参数
- **epochs**: 300 (从800降低)
- **batch_size**: 8 (从16降低，减少内存使用)
- **save_period**: 25 (更频繁保存)

### 蒸馏超参数 (已优化)
- **hyp_T**: 0.1 (温度参数)
- **hyp_box_distill**: 0.50 (边界框蒸馏权重，从0.70降低)
- **hyp_cls_distill**: 0.80 (分类蒸馏权重，从1.10降低)
- **hyp_dfl_distill**: 0.70 (DFL蒸馏权重，从1.00降低)
- **教师权重比例**: 0.95 (从0.99降低)

## 🔍 监控指标

### 训练过程中需要关注:
1. **损失收敛**: 总损失应逐渐降低到2.0以下
2. **权重匹配率**: 应该>80%
3. **内存使用**: 监控GPU内存，避免OOM
4. **mAP指标**: 在验证集上的性能

### 预期结果:
- 初始损失: 可能较高 (>5.0)
- 收敛损失: <2.0
- 训练稳定性: 无NaN或异常值
- 推理正常: 能够正确检测COCO类别

## 🛠️ 故障排除

### 常见问题:
1. **形状不匹配**: 检查层映射关系是否正确
2. **损失不收敛**: 调整蒸馏超参数
3. **内存不足**: 降低batch_size
4. **权重加载失败**: 重新运行权重转换

### 调试命令:
```bash
# 检查模型结构
python -c "from ultralytics import YOLO; model = YOLO('yolov8-lite.yaml'); print(model.model)"

# 检查权重文件
python -c "import torch; w = torch.load('runs/train/teacher_prepare.pt'); print(f'参数数量: {len(w)}')"

# 测试数据集加载
python -c "from ultralytics import YOLO; model = YOLO('yolov8n.pt'); model.val(data='coco128.yaml')"
```

## 📈 性能优化建议

1. **如果损失不收敛**:
   - 降低学习率
   - 调整蒸馏权重比例
   - 增加warmup epochs

2. **如果训练太慢**:
   - 使用更小的输入尺寸
   - 减少batch_size
   - 使用混合精度训练

3. **如果精度不够**:
   - 增加训练epochs
   - 使用数据增强
   - 调整损失函数权重

## ✅ 验证清单

- [ ] 权重转换成功 (phase=0)
- [ ] 模型加载无错误
- [ ] 权重匹配率>80%
- [ ] 训练开始正常 (phase=1)
- [ ] 损失逐渐下降
- [ ] 无内存溢出
- [ ] 推理结果正确

## 📞 下一步

训练完成后，可以进行:
1. 模型评估和对比
2. 导出ONNX格式
3. 部署测试
4. 性能基准测试
