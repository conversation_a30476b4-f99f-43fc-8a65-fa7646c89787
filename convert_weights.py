#!/usr/bin/env python3
"""
独立的权重转换脚本，用于将标准YOLOv8权重转换为知识蒸馏模型格式
"""

import torch
from ultralytics import YOLO
from Globals import teacher_peer_list, student_peer_list, path_teacher, path_teacher_prepare, path_student, path_student_prepare

def convert_teacher_weights(path_teacher, path_teacher_prepare):
    """
    将标准YOLOv8教师模型权重转换为知识蒸馏模型格式
    标准YOLOv8模型层编号0-21 -> 知识蒸馏模型教师层编号1-22
    """
    print(f"🔄 开始转换教师模型权重: {path_teacher}")
    save_state = {}
    
    try:
        model_teacher = YOL<PERSON>(path_teacher)
        teacher_state_dict = model_teacher.model.state_dict()
        print(f"📊 教师模型参数数量: {len(teacher_state_dict)}")
    except Exception as e:
        print(f"❌ 加载教师模型失败: {e}")
        return False

    converted_count = 0
    skipped_count = 0

    for param_tensor in teacher_state_dict:
        try:
            # 检查参数名是否包含层数信息
            parts = param_tensor.split('.')
            if len(parts) >= 3 and parts[2].isdigit():
                old_layer_number = int(parts[2])

                # 标准YOLOv8模型层编号0-21映射到教师层1-22
                if old_layer_number < len(teacher_peer_list):
                    new_layer_number = str(teacher_peer_list[old_layer_number])
                    new_param_name = param_tensor.replace("." + str(old_layer_number) + ".", "." + new_layer_number + ".", 1)
                    save_state[new_param_name] = teacher_state_dict[param_tensor]
                    converted_count += 1
                    if converted_count <= 5:  # 只显示前5个转换示例
                        print(f"✅ 层映射: {param_tensor} -> {new_param_name}")
                elif old_layer_number == 22:
                    # 标准模型的检测头(第22层)不直接映射，因为知识蒸馏模型有专门的检测头结构
                    print(f"⚠️  跳过检测头权重: {param_tensor}")
                    skipped_count += 1
                else:
                    print(f"⚠️  层数超出映射范围: {param_tensor}")
                    skipped_count += 1
            else:
                # 不是层参数，直接复制（如stride等全局参数）
                save_state[param_tensor] = teacher_state_dict[param_tensor]
                converted_count += 1

        except Exception as e:
            print(f"❌ 处理参数 {param_tensor} 时出错: {e}")
            skipped_count += 1

    print(f"✅ 权重转换完成: {converted_count} 个参数转换, {skipped_count} 个参数跳过")
    print(f"📊 转换后参数数量: {len(save_state)}")

    try:
        torch.save(save_state, path_teacher_prepare)
        print(f"💾 教师权重已保存到: {path_teacher_prepare}")
        return True
    except Exception as e:
        print(f"❌ 保存权重失败: {e}")
        return False

def convert_student_weights(path_student, path_student_prepare):
    """
    将标准YOLOv8学生模型权重转换为知识蒸馏模型格式
    标准YOLOv8模型层编号0-21 -> 知识蒸馏模型学生层编号23-44
    """
    print(f"🔄 开始转换学生模型权重: {path_student}")
    save_state = {}
    
    try:
        model_student = YOLO(path_student)
        student_state_dict = model_student.model.state_dict()
        print(f"📊 学生模型参数数量: {len(student_state_dict)}")
    except Exception as e:
        print(f"❌ 加载学生模型失败: {e}")
        return False

    converted_count = 0
    skipped_count = 0

    for param_tensor in student_state_dict:
        try:
            # 检查参数名是否包含层数信息
            parts = param_tensor.split('.')
            if len(parts) >= 3 and parts[2].isdigit():
                old_layer_number = int(parts[2])

                # 标准YOLOv8模型层编号0-21映射到学生层23-44
                if old_layer_number < len(student_peer_list):
                    new_layer_number = str(student_peer_list[old_layer_number])
                    new_param_name = param_tensor.replace("." + str(old_layer_number) + ".", "." + new_layer_number + ".", 1)
                    save_state[new_param_name] = student_state_dict[param_tensor]
                    converted_count += 1
                    if converted_count <= 5:  # 只显示前5个转换示例
                        print(f"✅ 层映射: {param_tensor} -> {new_param_name}")
                elif old_layer_number == 22:
                    # 标准模型的检测头(第22层)不直接映射
                    print(f"⚠️  跳过检测头权重: {param_tensor}")
                    skipped_count += 1
                else:
                    print(f"⚠️  层数超出映射范围: {param_tensor}")
                    skipped_count += 1
            else:
                # 不是层参数，直接复制
                save_state[param_tensor] = student_state_dict[param_tensor]
                converted_count += 1

        except Exception as e:
            print(f"❌ 处理参数 {param_tensor} 时出错: {e}")
            skipped_count += 1

    print(f"✅ 权重转换完成: {converted_count} 个参数转换, {skipped_count} 个参数跳过")
    print(f"📊 转换后参数数量: {len(save_state)}")

    try:
        torch.save(save_state, path_student_prepare)
        print(f"💾 学生权重已保存到: {path_student_prepare}")
        return True
    except Exception as e:
        print(f"❌ 保存权重失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始权重转换过程...")
    
    # 转换教师权重
    print("\n" + "="*50)
    print("转换教师模型权重")
    print("="*50)
    teacher_success = convert_teacher_weights(path_teacher, path_teacher_prepare)
    
    # 可选：转换学生权重
    print("\n" + "="*50)
    print("转换学生模型权重（可选）")
    print("="*50)
    student_success = convert_student_weights(path_student, path_student_prepare)
    
    print("\n" + "="*50)
    print("权重转换总结")
    print("="*50)
    print(f"教师权重转换: {'✅ 成功' if teacher_success else '❌ 失败'}")
    print(f"学生权重转换: {'✅ 成功' if student_success else '❌ 失败'}")
    
    if teacher_success:
        print("\n🎉 权重转换完成！现在可以设置 phase=1 开始知识蒸馏训练。")
    else:
        print("\n❌ 权重转换失败，请检查错误信息并重试。")
