#!/usr/bin/env python3
"""
知识蒸馏测试脚本，用于验证模型配置和权重加载是否正确
"""

import torch
from ultralytics import YOLO
from Globals import *

def test_model_loading():
    """测试模型加载和权重匹配"""
    print("🔄 测试知识蒸馏模型加载...")
    
    try:
        # 加载知识蒸馏模型架构
        model = YOLO(model_file)
        print(f"✅ 模型架构加载成功")
        print(f"📊 模型参数数量: {sum(p.numel() for p in model.model.parameters())}")
        
        # 检查模型结构
        print(f"📊 模型层数: {len(list(model.model.model))}")
        
        # 测试教师权重加载
        if torch.cuda.is_available():
            print(f"🔥 CUDA可用，设备: {torch.cuda.get_device_name()}")
        else:
            print("⚠️  CUDA不可用，将使用CPU")
            
        return True
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return False

def test_weight_loading():
    """测试权重加载"""
    print("\n🔄 测试权重加载...")
    
    try:
        # 检查权重文件是否存在
        import os
        if not os.path.exists(path_teacher_prepare):
            print(f"❌ 教师权重文件不存在: {path_teacher_prepare}")
            print("请先运行 python convert_weights.py 转换权重")
            return False
            
        # 加载模型
        model = YOLO(model_file)
        
        # 加载教师权重
        teacher_weights = torch.load(path_teacher_prepare, map_location='cpu')
        print(f"📊 教师权重文件包含 {len(teacher_weights)} 个参数")
        
        # 检查权重匹配情况
        model_dict = model.model.state_dict()
        print(f"📊 目标模型包含 {len(model_dict)} 个参数")
        
        matched_params = 0
        mismatched_params = 0
        missing_params = 0
        
        for name, param in teacher_weights.items():
            if name in model_dict:
                if param.shape == model_dict[name].shape:
                    matched_params += 1
                else:
                    mismatched_params += 1
                    if mismatched_params <= 3:  # 只显示前3个不匹配的例子
                        print(f"⚠️  形状不匹配: {name} - 教师: {param.shape}, 目标: {model_dict[name].shape}")
            else:
                missing_params += 1
        
        total_teacher_params = len(teacher_weights)
        match_rate = (matched_params / total_teacher_params) * 100 if total_teacher_params > 0 else 0
        
        print(f"📊 权重匹配统计:")
        print(f"   ✅ 匹配: {matched_params}")
        print(f"   ⚠️  形状不匹配: {mismatched_params}")
        print(f"   ❌ 缺失: {missing_params}")
        print(f"   📈 匹配率: {match_rate:.1f}%")
        
        if match_rate > 80:
            print("✅ 权重匹配率良好，可以开始训练")
            return True
        elif match_rate > 50:
            print("⚠️  权重匹配率中等，建议检查层映射关系")
            return True
        else:
            print("❌ 权重匹配率过低，需要修复层映射关系")
            return False
            
    except Exception as e:
        print(f"❌ 权重加载测试失败: {e}")
        return False

def test_dataset_config():
    """测试数据集配置"""
    print("\n🔄 测试数据集配置...")
    
    try:
        import os
        from ultralytics.utils import yaml_load
        
        # 检查数据集配置文件
        dataset_path = f"ultralytics/cfg/datasets/{dataset}"
        if not os.path.exists(dataset_path):
            print(f"❌ 数据集配置文件不存在: {dataset_path}")
            return False
            
        # 加载数据集配置
        data_config = yaml_load(dataset_path)
        print(f"✅ 数据集配置加载成功: {dataset}")
        print(f"📊 类别数: {data_config.get('nc', 'unknown')}")
        print(f"📂 数据路径: {data_config.get('path', 'unknown')}")
        
        # 检查数据路径是否存在
        data_path = data_config.get('path')
        if data_path and os.path.exists(data_path):
            print(f"✅ 数据路径存在: {data_path}")
        else:
            print(f"⚠️  数据路径不存在或未配置: {data_path}")
            
        return True
        
    except Exception as e:
        print(f"❌ 数据集配置测试失败: {e}")
        return False

def print_config_summary():
    """打印配置摘要"""
    print("\n" + "="*60)
    print("知识蒸馏配置摘要")
    print("="*60)
    print(f"模型文件: {model_file}")
    print(f"数据集: {dataset}")
    print(f"训练轮数: {epochs}")
    print(f"批次大小: {batch_size}")
    print(f"设备: {device}")
    print(f"蒸馏模式: {'开启' if bool_distill else '关闭'}")
    print(f"当前阶段: phase={phase}")
    print("\n蒸馏超参数:")
    print(f"  温度参数: {hyp_T}")
    print(f"  边界框蒸馏权重: {hyp_box_distill}")
    print(f"  分类蒸馏权重: {hyp_cls_distill}")
    print(f"  DFL蒸馏权重: {hyp_dfl_distill}")
    print(f"  教师权重比例: cls={hyp_w_t_cls}, box={hyp_w_t_box}, dfl={hyp_w_t_dfl}")
    print("="*60)

if __name__ == "__main__":
    print("🚀 开始知识蒸馏配置测试...")
    
    # 打印配置摘要
    print_config_summary()
    
    # 运行测试
    tests = [
        ("模型加载测试", test_model_loading),
        ("权重加载测试", test_weight_loading),
        ("数据集配置测试", test_dataset_config),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        result = test_func()
        results.append((test_name, result))
    
    # 总结测试结果
    print("\n" + "="*60)
    print("测试结果总结")
    print("="*60)
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "="*60)
    if all_passed:
        print("🎉 所有测试通过！配置正确，可以开始知识蒸馏训练。")
        print("💡 建议步骤:")
        print("   1. 如果还没有转换权重，运行: python convert_weights.py")
        print("   2. 设置 phase=1 在 Globals.py 中")
        print("   3. 运行: python main.py")
    else:
        print("❌ 部分测试失败，请根据上述错误信息修复配置。")
    print("="*60)
