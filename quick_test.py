#!/usr/bin/env python3
"""
快速测试知识蒸馏配置的脚本
"""

import torch
from ultralytics import YOLO
from Globals import *
from test_for_function import test_for_exchange_teacher

def main():
    print("🚀 开始知识蒸馏快速测试...")
    
    # 步骤1: 转换权重 (phase=0)
    print("\n" + "="*50)
    print("步骤1: 转换教师权重")
    print("="*50)
    
    success = test_for_exchange_teacher(path_teacher, path_teacher_prepare)
    if not success:
        print("❌ 权重转换失败，退出测试")
        return
    
    # 步骤2: 测试权重加载 (phase=1)
    print("\n" + "="*50)
    print("步骤2: 测试权重加载")
    print("="*50)
    
    try:
        # 加载知识蒸馏模型
        model = YOLO(model_file)
        print(f"✅ 模型架构加载成功")
        
        # 加载教师权重
        teacher_weights = torch.load(path_teacher_prepare, map_location='cpu')
        print(f"📊 教师权重文件包含 {len(teacher_weights)} 个参数")
        
        # 检查权重匹配
        model_dict = model.model.state_dict()
        matched = 0
        mismatched = 0
        
        for name, param in teacher_weights.items():
            if name in model_dict:
                if param.shape == model_dict[name].shape:
                    matched += 1
                else:
                    mismatched += 1
                    if mismatched <= 3:
                        print(f"⚠️  形状不匹配: {name}")
        
        total = len(teacher_weights)
        match_rate = (matched / total) * 100 if total > 0 else 0
        print(f"📊 权重匹配率: {match_rate:.1f}% ({matched}/{total})")
        
        if match_rate > 80:
            print("✅ 权重匹配良好，可以开始训练")
            
            # 步骤3: 简单训练测试
            print("\n" + "="*50)
            print("步骤3: 简单训练测试")
            print("="*50)
            
            # 加载权重到模型
            model.model.load_state_dict(teacher_weights, strict=False)
            print("✅ 权重加载完成")
            
            # 设置训练参数
            train_args = {
                'data': dataset,
                'epochs': 2,  # 只训练2个epoch进行测试
                'batch': 4,   # 小批次
                'device': device,
                'verbose': True,
                'save': False,  # 不保存模型
            }
            
            print("🔄 开始简单训练测试...")
            try:
                results = model.train(**train_args)
                print("✅ 训练测试成功完成！")
                print("🎉 知识蒸馏配置正确，可以进行正式训练")
                
                # 显示建议的下一步
                print("\n💡 建议的下一步:")
                print("1. 在 Globals.py 中设置 phase=1")
                print("2. 调整训练参数 (epochs, batch_size等)")
                print("3. 运行 python main.py 开始正式训练")
                print("4. 监控损失值，确保收敛到2.0以下")
                
            except Exception as e:
                print(f"⚠️  训练测试遇到问题: {e}")
                print("这可能是正常的，因为知识蒸馏需要特殊的训练逻辑")
                print("建议直接使用 main.py 进行训练")
        else:
            print("❌ 权重匹配率过低，需要检查层映射关系")
            
    except Exception as e:
        print(f"❌ 权重加载测试失败: {e}")

if __name__ == "__main__":
    main()
